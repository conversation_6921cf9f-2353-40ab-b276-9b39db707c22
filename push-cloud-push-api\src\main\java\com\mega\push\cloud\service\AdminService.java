package com.mega.push.cloud.service;

import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.push.cloud.PushCode;
import com.mega.push.cloud.PushException;
import com.mega.push.cloud.common.entity.ApplicationAuth;
import com.mega.push.cloud.common.entity.ApplicationInfo;
import com.mega.push.cloud.common.entity.ConfigAndroid;
import com.mega.push.cloud.common.entity.ConfigIos;
import com.mega.push.cloud.common.mapper.ApplicationAuthMapper;
import com.mega.push.cloud.common.mapper.ApplicationInfoMapper;
import com.mega.push.cloud.common.mapper.ConfigAndroidMapper;
import com.mega.push.cloud.common.mapper.ConfigIosMapper;
import com.mega.push.cloud.config.Config;
import com.mega.push.cloud.connection.manager.ConnectionManager;
import com.mega.push.cloud.core.utils.JsonUtils;
import com.mega.push.cloud.dao.AdminDao;
import com.mega.push.cloud.po.AuthPO;
import com.mega.push.cloud.util.AuthTokenUtil;
import com.mega.push.cloud.vo.AuthVO;
import com.xiaomi.xmpush.server.Constants;
import com.xiaomi.xmpush.server.Sender;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.event.EventListener;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
@Service
public class AdminService {
    private final ApplicationInfoMapper applicationInfoMapper;
    private final ApplicationAuthMapper applicationAuthMapper;
    private final ConfigIosMapper configIosMapper;
    private final ConfigAndroidMapper configAndroidMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final AdminDao adminDao;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    private String getEnvironment() {
        return activeProfile.equals("dev") ? "dev" : "prod";
    }

    @Autowired
    public AdminService(ApplicationInfoMapper applicationInfoMapper,
            ApplicationAuthMapper applicationAuthMapper,
            ConfigIosMapper configIosMapper,
            ConfigAndroidMapper configAndroidMapper,
            StringRedisTemplate stringRedisTemplate, AdminDao adminDao) {
        this.applicationInfoMapper = applicationInfoMapper;
        this.applicationAuthMapper = applicationAuthMapper;
        this.configIosMapper = configIosMapper;
        this.configAndroidMapper = configAndroidMapper;
        this.stringRedisTemplate = stringRedisTemplate;
        this.adminDao = adminDao;
    }

    @Transactional(rollbackFor = Exception.class)
    public AuthVO auth(AuthPO po) throws IOException, InterruptedException {
        // 1 查询应用授权
        ApplicationAuth applicationAuth = this.adminDao.getApplicationAuthByKeyAndSecret(po.getAuthKey(), po.getAuthSecret());
        if (applicationAuth == null) {
            throw new PushException(PushCode.PUSH_APPLICARTION_AUTH_ERROR);
        }

        // 2 查询应用信息
        ApplicationInfo applicationInfo = this.adminDao.getApplicationInfoById(applicationAuth.getApplicationInfoId());
        if (applicationInfo == null) {
            throw new PushException(PushCode.PUSH_APPLICARTION_INFO_ERROR);
        }

        // 3 获取推送配置
        getPushConfig(applicationInfo.getId());
        AuthVO authVO = new AuthVO();
        authVO.setAppId(applicationInfo.getId());
        authVO.setAppName(applicationInfo.getName());
        authVO.setAuthKey(applicationAuth.getAppkey());
        authVO.setAuthSecret(applicationAuth.getAppscret());
        if(getEnvironment().equals("dev")){
            authVO.setAuthToken(AuthTokenUtil.createToken(applicationAuth, 60*24)); // 测试环境token有效时间1天
            log.info("测试环境token有效时间1天");
        }else{
            authVO.setAuthToken(AuthTokenUtil.createToken(applicationAuth, 15)); // 生产环境token有效时间15分钟
        }        
        
        return authVO;
    }

    /**
     * 项目启动时执行
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        try {
            log.info("------------------------application config init  start!------------------------------");
            // 尝试初始化小米推送SDK，如果失败则记录日志但不中断启动
            try {
                Constants.useOfficial();
                log.info("小米推送SDK初始化成功");
            } catch (Exception e) {
                log.warn("小米推送SDK初始化失败，将跳过小米推送功能: {}", e.getMessage());
            }
            // 初始化所有应用配置
            List<ApplicationInfo> applicationInfoList = this.applicationInfoMapper.selectAll();
            for (ApplicationInfo appInfo : applicationInfoList) {
                getPushConfig(appInfo.getId());
            }
            log.info("------------------------application config init  success!------------------------------");
        } catch (Exception e) {
            log.error("Failed to initialize config", e);
        }
    }

    public Config getPushConfig(Long applicationId) throws IOException, InterruptedException {
        Config config = new Config();
        String apIdStr = applicationId + "";
        if (ConnectionManager.configMap.containsKey(apIdStr) && ConnectionManager.configMap.get(apIdStr) != null) {
            config = ConnectionManager.configMap.get(apIdStr);
            return config;
        }

        // 1 查询美嘉平台应用记录
        List<ApplicationAuth> applicationAuthList = this.adminDao.getApplicationAuthByAppId(applicationId);
        if (applicationAuthList == null || applicationAuthList.size() < 0) {
            throw new PushException(PushCode.PUSH_APPLICARTION_INFO_ERROR);
        }

        // key = appscret
        Map<String, ApplicationAuth> applicationAuthMap = new ConcurrentHashMap<>();
        // 2 遍历配置
        for (ApplicationAuth auth : applicationAuthList) {
            applicationAuthMap.put(auth.getAppscret(), auth);

            // ios
            if (auth.getType().equals(0)) {
                List<ConfigIos> configIosList = this.adminDao.getConfigIosByAuthId(auth.getId());
                if (configIosList == null || configIosList.size() < 1) {
                    log.error("config IOS 使用application_auth表id:{} 查询config_ios表为空", auth.getId());
                    continue;
                    // throw new PushException(PushCode.PUSH_APPLICARTION_AUTH_ERROR);
                }

                for (ConfigIos configIos1 : configIosList) {
                    String appIdAuthId = configIos1.getAppId() + "." + configIos1.getApplicationAuthId();
                    if (!ConnectionManager.apnsClientMap
                            .containsKey(appIdAuthId) ||
                            ConnectionManager.apnsClientMap
                                    .get(appIdAuthId) == null) {
                        ConnectionManager.createApnsClient(configIos1);
                    }

                    String appIdBusiId = configIos1.getAppId() + "." + configIos1.getBusiId();
                    if (!ConnectionManager.pushNotificationMap
                            .containsKey(appIdBusiId) ||
                            ConnectionManager.pushNotificationMap
                                    .get(appIdBusiId) == null) {
                        ConnectionManager.pushNotificationMap.put(appIdBusiId, new ConcurrentLinkedDeque<>());
                        ConnectionManager.apnsAppMap.put(configIos1);
                    }
                }
                config.setConfigIosList(configIosList);
            } else {
                getAndroidConfig(auth, config);
            }
        }
        config.setApplicationAuthMap(applicationAuthMap);
        ConnectionManager.configMap.put(applicationId + "", config);

        return config;
    }

    private void getAndroidConfig(ApplicationAuth auth, Config config) throws JsonProcessingException {
        // 根据auth_id查询android配置
        List<ConfigAndroid> configAndroidList = this.adminDao.getConfigAndroidByAuthId(auth.getId());
        log.info("config Android :" + JsonUtils.toJson(configAndroidList));
        // 推送队列初始化
        if (configAndroidList == null || configAndroidList.size() < 1) {
            log.error("config Android 使用application_auth表id:{} 查询config_android表为空", auth.getId());
            throw new PushException(PushCode.PUSH_APPLICARTION_AUTH_ERROR);
        }
        config.setConfigAndroidList(configAndroidList);

        for (ConfigAndroid item : configAndroidList) {
            String configId = item.getId().toString();
            if (!ConnectionManager.androidPushNotificationMap.containsKey(configId)
                    || ConnectionManager.androidPushNotificationMap.get(configId) == null) {
                ConnectionManager.androidPushNotificationMap.put(configId, new ConcurrentLinkedDeque<>());
            }
            switch (auth.getType()) {
                case 2: { // 华为
                    // if
                    // (!ConnectionManager.huaWeiClientMap.containsKey(item.getId().intValue()+"")
                    // ||
                    // ConnectionManager.huaWeiClientMap.get(item.getId().intValue()+"")
                    // == null) {
                    // ConnectionManager.huaWeiClientMap.put(item.getId().intValue() + "",
                    // ConnectionManager.createHWClient(item));
                    // }
                    log.info("华为推送暂未接入");
                }
                    break;

                case 3: { // 小米
                    log.info("小米推送配置 configId: {}, 包名：{}", configId, item.getPackageName());
                    // if (!ConnectionManager.xiaoMiClientMap.containsKey(configId)
                    //         || ConnectionManager.xiaoMiClientMap.get(configId) == null) {
                    //     log.info("创建小米推送客户端 configId:" + configId);
                    //     ConnectionManager.xiaoMiClientMap.put(configId, ConnectionManager.createMiClient(item));
                    // }
                }
                    break;

                default: {
                }
                    break;
            }
        }
    }

}
