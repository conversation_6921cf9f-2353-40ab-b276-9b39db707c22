FROM openjdk:8-alpine
ARG JAR_FILE
ENV MEM 512m
ENV CONFIG ""
WORKDIR /opt
RUN sed -i "s/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g" /etc/apk/repositories
RUN apk add tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
# 复制证书目录，保持完整的目录结构
COPY cerpath/ ./cerpath/
ADD push-cloud-push-api/target/${JAR_FILE} app.jar
ENTRYPOINT java -Xms${MEM} -Xmx${MEM} -jar app.jar ${CONFIG}