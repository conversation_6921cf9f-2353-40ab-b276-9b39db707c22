logging:
  file:
    path: /opt/log/${spring.application.name}-${server.port}/
    max-size: 128MB
spring:
  cloud:
    consul:
      enabled: true
      host: *************
      port: 9910
      discovery:
        health-check-enabled: false
        prefer-ip-address: true
        ip-address: *************
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.ip-address}-${server.port}
        service-name: ${spring.application.name}
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: admin
    password: Mangosteen0!
    maximum-pool-size: 10
  redis:
    enabled: false
  cache: none
management:
    health:
      redis:
        enabled: false