#!/bin/bash

# 验证Docker镜像中cerpath目录的脚本
# 使用方法: ./verify-cerpath.sh <image_name>

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <docker_image_name>"
    echo "例如: $0 172.25.220.217:1180/push-cloud/push-cloud-push-api/test:latest"
    exit 1
fi

IMAGE_NAME=$1

echo "正在验证Docker镜像: $IMAGE_NAME"
echo "检查cerpath目录是否存在..."

# 运行容器并检查cerpath目录
docker run --rm $IMAGE_NAME sh -c "
echo '=== 检查工作目录 ==='
pwd
echo ''
echo '=== 检查cerpath目录是否存在 ==='
if [ -d './cerpath' ]; then
    echo '✓ cerpath目录存在'
    echo ''
    echo '=== cerpath目录结构 ==='
    find ./cerpath -type f -name '*.p12' | head -20
    echo ''
    echo '=== cerpath目录详细信息 ==='
    ls -la ./cerpath/
    echo ''
    echo '=== 各子目录内容 ==='
    for dir in ./cerpath/*/; do
        if [ -d \"\$dir\" ]; then
            echo \"目录: \$dir\"
            ls -la \"\$dir\"
            echo ''
        fi
    done
else
    echo '✗ cerpath目录不存在'
    echo '当前目录内容:'
    ls -la
fi
"

echo "验证完成"
