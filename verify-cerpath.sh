#!/bin/bash

# 验证Docker镜像中cerpath目录和lib目录的脚本
# 使用方法: ./verify-cerpath.sh <image_name>

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <docker_image_name>"
    echo "例如: $0 172.25.220.217:1180/push-cloud/push-cloud-push-api/test:latest"
    exit 1
fi

IMAGE_NAME=$1

echo "正在验证Docker镜像: $IMAGE_NAME"
echo "检查cerpath目录和lib目录是否存在..."

# 运行容器并检查目录
docker run --rm $IMAGE_NAME sh -c "
echo '=== 检查工作目录 ==='
pwd
echo ''
echo '=== 检查cerpath目录是否存在 ==='
if [ -d './cerpath' ]; then
    echo '✓ cerpath目录存在'
    echo ''
    echo '=== cerpath目录结构 ==='
    find ./cerpath -type f -name '*.p12' | head -20
    echo ''
    echo '=== cerpath目录详细信息 ==='
    ls -la ./cerpath/
else
    echo '✗ cerpath目录不存在'
fi
echo ''
echo '=== 检查lib目录是否存在 ==='
if [ -d './lib' ]; then
    echo '✓ lib目录存在'
    echo ''
    echo '=== lib目录内容 ==='
    ls -la ./lib/
    echo ''
    echo '=== 检查小米推送SDK JAR文件 ==='
    if [ -f './lib/MiPush_SDK_Server_Http2_1.0.14.jar' ]; then
        echo '✓ 小米推送SDK JAR文件存在'
    else
        echo '✗ 小米推送SDK JAR文件不存在'
    fi
    if [ -f './lib/json-simple-1.1.1.jar' ]; then
        echo '✓ json-simple JAR文件存在'
    else
        echo '✗ json-simple JAR文件不存在'
    fi
else
    echo '✗ lib目录不存在'
fi
echo ''
echo '=== 当前目录所有内容 ==='
ls -la
echo ''
echo '=== 测试Java classpath ==='
java -cp 'app.jar:lib/*' -version 2>&1 | head -3
"

echo "验证完成"
